# RunVSAgent 技术架构详解

## 📋 目录

- [架构概览](#架构概览)
- [核心设计原则](#核心设计原则)
- [系统架构](#系统架构)
- [核心组件](#核心组件)
- [通信机制](#通信机制)
- [数据流分析](#数据流分析)
- [扩展机制](#扩展机制)
- [性能优化](#性能优化)

## 🏗️ 架构概览

RunVSAgent采用**分层代理架构**，通过RPC通信桥接VSCode扩展生态与JetBrains IDE平台。整体架构遵循**SOLID原则**，确保高内聚、低耦合的设计。

### 设计目标

- **兼容性**: 最大化VSCode扩展API兼容性
- **性能**: 低延迟的实时通信和响应
- **稳定性**: 进程隔离和错误恢复机制
- **扩展性**: 支持多种IDE和扩展类型

## 🎯 核心设计原则

### KISS (Keep It Simple, Stupid)
- 每个组件职责单一明确
- 避免过度设计和复杂抽象
- 优先使用成熟的通信协议

### YAGNI (You Aren't Gonna Need It)
- 只实现当前需要的VSCode API
- 按需加载扩展和服务
- 渐进式功能扩展

### SOLID原则
- **单一职责**: 每个类只负责一个功能领域
- **开闭原则**: 通过接口扩展，不修改核心代码
- **里氏替换**: 不同IDE实现可互相替换
- **接口隔离**: 细粒度的服务接口设计
- **依赖倒置**: 依赖抽象而非具体实现

## 🏛️ 系统架构

```mermaid
graph TB
    subgraph "JetBrains IDE Process"
        A[Kotlin Plugin]
        B[UI Manager]
        C[Editor Bridge]
        D[WebView Manager]
        E[RPC Client]
    end
    
    subgraph "Extension Host Process"
        F[Node.js Runtime]
        G[VSCode API Layer]
        H[Extension Manager]
        I[RPC Server]
        J[Service Registry]
    end
    
    subgraph "VSCode Extensions"
        K[Roo Code Agent]
        L[Language Servers]
        M[Debug Adapters]
    end
    
    A --> B
    A --> C
    A --> D
    B --> E
    C --> E
    D --> E
    
    E <-->|RPC/IPC| I
    
    I --> F
    F --> G
    F --> H
    F --> J
    
    G --> K
    G --> L
    G --> M
    H --> K
    H --> L
    H --> M
```

## 🔧 核心组件

### 1. JetBrains Plugin Layer (Kotlin)

#### WecoderPlugin
- **职责**: 插件生命周期管理和初始化
- **核心功能**:
  - 项目级服务注册
  - 扩展进程启动协调
  - 资源清理和释放

```kotlin
@Service(Service.Level.PROJECT)
class WecoderPluginService(private var currentProject: Project) : Disposable {
    private val socketServer = ExtensionSocketServer()
    private val processManager = ExtensionProcessManager()
    private val rpcManager = RPCManager()
}
```

#### ExtensionProcessManager
- **职责**: Extension Host进程管理
- **核心功能**:
  - Node.js进程启动和监控
  - 环境变量配置
  - 进程异常恢复

#### RPC通信层
- **协议**: 基于MessagePack的二进制协议
- **传输**: Unix Domain Socket (Linux/macOS) / Named Pipe (Windows)
- **特性**: 双向异步通信、请求/响应模式

### 2. Extension Host Layer (Node.js/TypeScript)

#### ExtensionManager
- **职责**: VSCode扩展生命周期管理
- **核心功能**:
  - 扩展发现和加载
  - 激活事件处理
  - 依赖关系解析

```typescript
export class ExtensionManager {
    private extensionDescriptions: Map<string, IExtensionDescription> = new Map();
    
    public registerExtension(extensionName: string): ExtensionRegistration {
        // 扩展注册逻辑
    }
    
    public activateExtension(extensionId: string, rpcProtocol: IRPCProtocol): Promise<void> {
        // 扩展激活逻辑
    }
}
```

#### VSCode API兼容层
- **实现范围**: 核心API的80%覆盖率
- **重点支持**:
  - `vscode.workspace` - 工作区管理
  - `vscode.window` - 窗口和UI交互
  - `vscode.languages` - 语言服务
  - `vscode.debug` - 调试支持

#### RPCManager
- **职责**: RPC协议管理和服务代理
- **核心功能**:
  - 服务注册和发现
  - 方法调用代理
  - 错误处理和重试

### 3. 通信协议层

#### 消息格式
```typescript
interface RPCMessage {
    type: 'request' | 'response' | 'notification';
    id?: number;
    method?: string;
    params?: any[];
    result?: any;
    error?: RPCError;
}
```

#### 服务代理机制
```typescript
// 主线程服务接口
interface MainThreadSearchShape {
    registerFileSearchProvider(handle: number, scheme: string): void;
    registerTextSearchProvider(handle: number, scheme: string): void;
}

// 扩展线程代理
class ExtHostSearch {
    private proxy: MainThreadSearchShape;
    
    registerSearchProvider(provider: SearchProvider): Disposable {
        const handle = this.addProvider(provider);
        this.proxy.registerFileSearchProvider(handle, provider.scheme);
        return new Disposable(() => this.proxy.unregisterProvider(handle));
    }
}
```

## 🔄 数据流分析

### 1. 扩展激活流程

```mermaid
sequenceDiagram
    participant IDE as JetBrains IDE
    participant Plugin as Kotlin Plugin
    participant Host as Extension Host
    participant Ext as VSCode Extension
    
    IDE->>Plugin: 项目打开
    Plugin->>Host: 启动Extension Host进程
    Host->>Plugin: 发送Ready消息
    Plugin->>Host: 发送初始化数据
    Host->>Ext: 激活扩展
    Ext->>Host: 注册服务和命令
    Host->>Plugin: 扩展就绪通知
    Plugin->>IDE: 显示扩展UI
```

### 2. 编辑器同步流程

```mermaid
sequenceDiagram
    participant Editor as IDE Editor
    participant Bridge as Editor Bridge
    participant RPC as RPC Layer
    participant API as VSCode API
    participant Ext as Extension
    
    Editor->>Bridge: 文档变更事件
    Bridge->>RPC: 序列化变更数据
    RPC->>API: 触发onDidChangeTextDocument
    API->>Ext: 通知扩展
    Ext->>API: 请求更新装饰器
    API->>RPC: 发送装饰器数据
    RPC->>Bridge: 反序列化数据
    Bridge->>Editor: 应用视觉效果
```

### 3. WebView通信流程

```mermaid
sequenceDiagram
    participant WebView as JetBrains WebView
    participant Manager as WebView Manager
    participant Host as Extension Host
    participant Panel as Extension Panel
    
    Panel->>Host: 创建WebView请求
    Host->>Manager: 发送WebView配置
    Manager->>WebView: 创建JCEF实例
    WebView->>Manager: 页面加载完成
    Manager->>Host: WebView就绪通知
    Host->>Panel: 返回WebView句柄
    
    Note over WebView,Panel: 双向消息通信
    Panel->>Host: postMessage
    Host->>Manager: 转发消息
    Manager->>WebView: JavaScript调用
```

## 🔌 扩展机制

### 1. 扩展发现和加载

```typescript
// 扩展描述解析
private parseExtensionDescription(extensionPath: string): IExtensionDescription {
    const packageJsonPath = path.join(extensionPath, 'extension.package.json');
    const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
    
    return {
        identifier: new ExtensionIdentifier(packageJson.name),
        name: packageJson.name,
        main: './extension.cjs',
        activationEvents: packageJson.activationEvents || ['onStartupFinished'],
        extensionLocation: URI.file(path.resolve(extensionPath)),
        // ... 其他属性
    };
}
```

### 2. 服务注册机制

```kotlin
// Kotlin端服务注册
private fun setupDefaultProtocols() {
    // 注册核心服务
    rpcProtocol.set(MainContext.MainThreadCommands, MainThreadCommands(project))
    rpcProtocol.set(MainContext.MainThreadWorkspace, MainThreadWorkspace(project))
    rpcProtocol.set(MainContext.MainThreadLanguages, MainThreadLanguages(project))
    rpcProtocol.set(MainContext.MainThreadSearch, MainThreadSearch())
}
```

### 3. API兼容性实现

```typescript
// VSCode API模拟实现
namespace vscode {
    export namespace workspace {
        export function openTextDocument(uri: Uri): Thenable<TextDocument> {
            // 通过RPC调用IDE的文档打开功能
            return rpcProtocol.getProxy(MainContext.MainThreadWorkspace)
                .openTextDocument(uri);
        }
    }
}
```

## ⚡ 性能优化

### 1. 通信优化

- **批量消息处理**: 合并多个小消息减少IPC开销
- **增量同步**: 只传输文档变更的差异部分
- **压缩传输**: 大数据使用gzip压缩

### 2. 内存管理

- **弱引用缓存**: 避免内存泄漏
- **按需加载**: 延迟加载非核心扩展
- **资源池化**: 复用昂贵的对象实例

### 3. 并发处理

```kotlin
// 异步处理避免阻塞UI线程
private val coroutineScope = CoroutineScope(Dispatchers.IO + SupervisorJob())

fun handleRPCRequest(request: RPCRequest) {
    coroutineScope.launch {
        try {
            val result = processRequest(request)
            sendResponse(request.id, result)
        } catch (e: Exception) {
            sendError(request.id, e)
        }
    }
}
```

## 🛡️ 错误处理和恢复

### 1. 进程隔离
- Extension Host崩溃不影响IDE稳定性
- 自动重启机制
- 状态恢复和重连

### 2. 优雅降级
- 扩展加载失败时的fallback机制
- 部分功能不可用时的用户提示
- 网络异常时的离线模式

### 3. 调试支持
- 详细的日志记录
- 性能监控和指标收集
- 开发模式的调试端口

## 🔮 扩展性设计

### 1. 多IDE支持
- 抽象的IDE接口层
- 插件化的IDE适配器
- 统一的配置管理

### 2. 扩展生态
- 标准的扩展API
- 扩展市场集成
- 版本兼容性管理

### 3. 云端集成
- 远程扩展支持
- 云端配置同步
- 分布式调试能力

---

这个架构设计确保了RunVSAgent既能提供强大的功能，又能保持良好的性能和稳定性，为跨IDE的扩展运行提供了坚实的技术基础。
