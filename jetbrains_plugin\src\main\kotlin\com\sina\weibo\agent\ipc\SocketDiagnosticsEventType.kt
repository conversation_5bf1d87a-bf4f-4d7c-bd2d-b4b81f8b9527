// SPDX-FileCopyrightText: 2025 Weibo, Inc.
//
// SPDX-License-Identifier: Apache-2.0

package com.sina.weibo.agent.ipc

/**
 * Socket diagnostics event type
 * Corresponds to SocketDiagnosticsEventType in VSCode
 */
enum class SocketDiagnosticsEventType {
    CREATED,
    READ,
    WRIT<PERSON>,
    <PERSON><PERSON><PERSON>,
    ERROR,
    <PERSON><PERSON><PERSON><PERSON>,
    
    BROWSER_WEB_SOCKET_BLOB_RECEIVED,
    
    NODE_END_RECEIVED,
    NODE_END_SENT,
    NODE_DRAIN_BEGIN,
    NODE_DRAIN_END,
    
    ZLIB_INFLATE_ERROR,
    <PERSON>LIB_INFLATE_DATA,
    ZLIB_INFLATE_INITIAL_WRITE,
    Z<PERSON><PERSON>_INFLATE_INITIAL_FLUSH_FIRED,
    ZLIB_INFLATE_WRITE,
    ZLIB_INFLATE_FLUSH_FIRED,
    ZLIB_DEFLATE_ERROR,
    ZLIB_DEFLATE_DATA,
    ZLIB_DEFLATE_WRITE,
    ZLIB_DEFLATE_FLUSH_FIRED,
    
    WEB_SOCKET_NODE_SOCKET_WRITE,
    WEB_SOCKET_NODE_SOCKET_PEEKED_HEADER,
    WEB_SOCKET_NODE_SOCKET_READ_HEADER,
    WEB_SOCKET_NODE_SOCKET_READ_DATA,
    WEB_SOCKET_NODE_SOCKET_UNMASKED_DATA,
    WEB_SOCKET_NODE_SOCKET_DRAIN_BEGIN,
    WEB_SOCKET_NODE_SOCKET_DRAIN_END,
    
    PROTOCOL_HEADER_READ,
    PROTOCOL_MESSAGE_READ,
    PROTOCOL_HEADER_WRITE,
    PROTOCOL_MESSAGE_WRITE,
    PROTOCOL_WRITE
} 