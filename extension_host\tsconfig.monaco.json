{"extends": "./tsconfig.base.json", "compilerOptions": {"noEmit": true, "types": ["@webgpu/types", "trusted-types", "wicg-file-system-access"], "paths": {}, "module": "amd", "moduleResolution": "node", "removeComments": false, "preserveConstEnums": true, "target": "ES2022", "sourceMap": false, "declaration": true}, "include": ["typings/css.d.ts", "typings/thenable.d.ts", "typings/vscode-globals-product.d.ts", "typings/vscode-globals-nls.d.ts", "typings/editContext.d.ts", "vs/monaco.d.ts", "vs/editor/*", "vs/base/common/*", "vs/base/browser/*", "vs/platform/*/common/*", "vs/platform/*/browser/*"], "exclude": ["node_modules/*", "vs/platform/files/browser/htmlFileSystemProvider.ts", "vs/platform/files/browser/webFileSystemAccess.ts", "vs/platform/telemetry/*", "vs/platform/assignment/*", "vs/platform/terminal/*", "vs/platform/externalTerminal/*"]}