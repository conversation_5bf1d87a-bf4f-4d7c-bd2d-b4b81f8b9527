# RunVSAgent 使用流程图

## 🚀 系统启动流程

### 完整启动时序图
```mermaid
sequenceDiagram
    participant U as 用户
    participant IDE as JetBrains IDE
    participant P as RunVSAgent插件
    participant EH as Extension Host
    participant VS as VSCode扩展
    participant FS as 文件系统
    
    Note over U,FS: 系统启动阶段
    U->>IDE: 启动JetBrains IDE
    IDE->>P: 加载插件
    P->>P: 初始化插件服务
    P->>FS: 检查Node.js环境
    FS-->>P: 环境验证结果
    
    Note over U,FS: Extension Host启动
    P->>EH: 启动Extension Host进程
    EH->>EH: 初始化Node.js运行时
    EH->>P: 发送Ready消息
    P->>EH: 发送初始化数据
    EH->>P: 发送Initialized消息
    
    Note over U,FS: RPC连接建立
    P->>EH: 建立RPC连接
    EH->>P: 确认连接成功
    
    Note over U,FS: 扩展激活
    P->>EH: 请求激活VSCode扩展
    EH->>VS: 激活扩展
    VS->>EH: 扩展就绪
    EH->>P: 激活完成通知
    
    Note over U,FS: 用户界面准备
    P->>IDE: 注册工具窗口
    P->>IDE: 更新UI状态
    IDE->>U: 显示RunVSAgent界面
```

## 📁 项目初始化流程

### 工作空间初始化
```mermaid
flowchart TD
    A[用户打开项目] --> B{检查项目类型}
    B -->|支持的语言| C[初始化工作空间]
    B -->|不支持| D[显示兼容性提示]
    
    C --> E[扫描项目文件]
    E --> F[配置语言服务]
    F --> G[加载项目设置]
    G --> H[启动文件监听]
    H --> I[通知扩展工作空间就绪]
    
    I --> J[AI助手可用]
    
    D --> K[用户选择]
    K -->|继续使用| L[基础功能模式]
    K -->|配置支持| M[引导配置页面]
    
    style C fill:#e1f5fe
    style J fill:#c8e6c9
    style L fill:#fff3e0
```

## 🤖 AI助手交互流程

### 用户与AI助手对话流程
```mermaid
sequenceDiagram
    participant U as 用户
    participant UI as IDE界面
    participant P as 插件核心
    participant WV as WebView
    participant AI as AI助手
    participant ED as 编辑器
    
    Note over U,ED: 用户发起对话
    U->>UI: 输入问题/指令
    UI->>P: 传递用户输入
    P->>WV: 更新WebView显示
    P->>AI: 发送AI请求
    
    Note over U,ED: AI处理阶段
    AI->>AI: 分析代码上下文
    AI->>AI: 生成响应内容
    AI->>P: 返回AI建议
    
    Note over U,ED: 结果展示
    P->>WV: 更新对话内容
    WV->>UI: 渲染AI回复
    
    Note over U,ED: 代码应用（可选）
    U->>WV: 选择应用建议
    WV->>P: 发送应用请求
    P->>ED: 修改代码
    ED->>U: 显示代码变更
```

### AI助手功能决策树
```mermaid
flowchart TD
    A[用户输入] --> B{输入类型识别}
    
    B -->|代码问题| C[代码分析流程]
    B -->|重构请求| D[代码重构流程]
    B -->|解释需求| E[代码解释流程]
    B -->|生成需求| F[代码生成流程]
    B -->|调试请求| G[调试辅助流程]
    
    C --> C1[获取当前文件上下文]
    C1 --> C2[分析代码结构]
    C2 --> C3[识别问题点]
    C3 --> C4[生成解决方案]
    
    D --> D1[分析重构范围]
    D1 --> D2[检查依赖关系]
    D2 --> D3[生成重构计划]
    D3 --> D4[逐步应用更改]
    
    E --> E1[提取目标代码]
    E1 --> E2[分析代码逻辑]
    E2 --> E3[生成详细解释]
    
    F --> F1[理解需求描述]
    F1 --> F2[选择实现方案]
    F2 --> F3[生成代码框架]
    F3 --> F4[完善实现细节]
    
    G --> G1[收集错误信息]
    G1 --> G2[分析调用堆栈]
    G2 --> G3[定位问题原因]
    G3 --> G4[提供修复建议]
    
    style A fill:#fff3e0
    style C4 fill:#c8e6c9
    style D4 fill:#c8e6c9
    style E3 fill:#c8e6c9
    style F4 fill:#c8e6c9
    style G4 fill:#c8e6c9
```

## 🔄 数据同步流程

### 文档变更同步
```mermaid
sequenceDiagram
    participant ED as JetBrains编辑器
    participant LIS as 文档监听器
    participant P as 插件核心
    participant EH as Extension Host
    participant VS as VSCode扩展
    
    Note over ED,VS: 用户编辑代码
    ED->>LIS: 文档内容变更
    LIS->>P: 触发变更事件
    P->>EH: 发送文档更新RPC
    EH->>VS: 通知扩展文档变更
    
    Note over ED,VS: 扩展响应变更
    VS->>EH: 请求最新文档内容
    EH->>P: 转发内容请求
    P->>ED: 获取当前文档状态
    ED->>P: 返回文档内容
    P->>EH: 发送文档数据
    EH->>VS: 提供最新内容
    
    Note over ED,VS: 扩展处理完成
    VS->>EH: 发送处理结果
    EH->>P: 转发结果
    P->>ED: 更新编辑器状态
```

### 项目文件监听流程
```mermaid
flowchart TD
    A[文件系统事件] --> B{事件类型判断}
    
    B -->|文件创建| C[新文件处理]
    B -->|文件修改| D[修改文件处理]
    B -->|文件删除| E[删除文件处理]
    B -->|文件重命名| F[重命名处理]
    
    C --> C1[检查文件类型]
    C1 --> C2[更新项目索引]
    C2 --> C3[通知相关扩展]
    
    D --> D1[检查文件变更类型]
    D1 --> D2[增量更新索引]
    D2 --> D3[触发相关处理器]
    
    E --> E1[清理文件索引]
    E1 --> E2[更新依赖关系]
    E2 --> E3[通知扩展清理]
    
    F --> F1[更新文件路径映射]
    F1 --> F2[修复引用关系]
    F2 --> F3[同步扩展状态]
    
    C3 --> G[完成处理]
    D3 --> G
    E3 --> G
    F3 --> G
    
    style A fill:#fff3e0
    style G fill:#c8e6c9
```

## 🖥️ WebView 渲染流程

### WebView生命周期管理
```mermaid
stateDiagram-v2
    [*] --> 初始化: 扩展注册WebView
    初始化 --> 资源准备: 解析扩展资源路径
    资源准备 --> 创建浏览器: 初始化JCEF浏览器
    创建浏览器 --> 设置拦截: 配置资源拦截器
    设置拦截 --> 加载内容: 加载HTML内容
    加载内容 --> 建立通信: 注入通信桥接
    建立通信 --> 就绪: WebView就绪
    
    就绪 --> 更新内容: 内容更新请求
    更新内容 --> 就绪: 更新完成
    
    就绪 --> 主题变更: 主题切换
    主题变更 --> 就绪: 主题应用完成
    
    就绪 --> 销毁: 扩展卸载
    销毁 --> [*]: 清理资源
    
    note right of 就绪: 正常运行状态\n可处理用户交互
```

### 资源加载优化流程
```mermaid
flowchart TD
    A[WebView请求资源] --> B{资源类型检查}
    
    B -->|HTML文件| C[HTML处理管道]
    B -->|CSS文件| D[CSS处理管道]
    B -->|JS文件| E[JS处理管道]
    B -->|图片资源| F[图片处理管道]
    B -->|字体文件| G[字体处理管道]
    
    C --> C1[解析vscode-file协议]
    C1 --> C2[转换为本地路径]
    C2 --> C3[注入主题变量]
    C3 --> C4[返回处理后HTML]
    
    D --> D1[读取CSS文件]
    D1 --> D2[应用主题覆盖]
    D2 --> D3[压缩CSS内容]
    D3 --> D4[返回优化后CSS]
    
    E --> E1[加载JS文件]
    E1 --> E2[注入API桥接代码]
    E2 --> E3[应用安全策略]
    E3 --> E4[返回安全JS]
    
    F --> F1[缓存检查]
    F1 --> F2{是否已缓存}
    F2 -->|是| F3[返回缓存图片]
    F2 -->|否| F4[加载并缓存图片]
    F4 --> F3
    
    G --> G1[字体格式检查]
    G1 --> G2[设置合适MIME类型]
    G2 --> G3[返回字体文件]
    
    C4 --> H[内容传递给WebView]
    D4 --> H
    E4 --> H
    F3 --> H
    G3 --> H
    
    style A fill:#fff3e0
    style H fill:#c8e6c9
```

## ⚡ 性能优化流程

### 启动性能优化
```mermaid
flowchart TD
    A[IDE启动] --> B[延迟初始化策略]
    B --> C{核心功能检查}
    
    C -->|立即需要| D[核心组件启动]
    C -->|可延迟| E[后台异步加载]
    
    D --> D1[启动Extension Host]
    D1 --> D2[建立RPC连接]
    D2 --> D3[注册基础服务]
    
    E --> E1[预加载扩展资源]
    E1 --> E2[预编译模板]
    E2 --> E3[缓存配置数据]
    
    D3 --> F[基础功能可用]
    E3 --> G[完整功能就绪]
    
    F --> H[用户可开始使用]
    G --> I[所有功能可用]
    
    style A fill:#fff3e0
    style H fill:#ffeb3b
    style I fill:#c8e6c9
```

### 内存管理优化流程
```mermaid
sequenceDiagram
    participant GC as 垃圾回收器
    participant MM as 内存管理器
    participant EM as 扩展管理器
    participant WM as WebView管理器
    
    Note over GC,WM: 定期内存检查
    GC->>MM: 触发内存检查
    MM->>EM: 检查扩展内存使用
    EM->>MM: 返回内存统计
    MM->>WM: 检查WebView内存
    WM->>MM: 返回WebView统计
    
    Note over GC,WM: 内存清理决策
    MM->>MM: 分析内存压力
    
    alt 内存压力较低
        MM->>GC: 标准清理策略
    else 内存压力中等
        MM->>EM: 清理非活跃扩展
        MM->>WM: 释放隐藏WebView
    else 内存压力较高
        MM->>EM: 卸载长时间未用扩展
        MM->>WM: 强制释放WebView资源
        MM->>GC: 强制垃圾回收
    end
    
    Note over GC,WM: 清理执行
    EM->>MM: 清理完成通知
    WM->>MM: 释放完成通知
    MM->>GC: 内存优化完成
```

## 🔧 错误处理流程

### 分层错误处理
```mermaid
flowchart TD
    A[错误发生] --> B{错误层级判断}
    
    B -->|UI层错误| C[用户界面错误处理]
    B -->|业务层错误| D[业务逻辑错误处理]
    B -->|通信层错误| E[RPC通信错误处理]
    B -->|系统层错误| F[系统级错误处理]
    
    C --> C1[显示用户友好消息]
    C1 --> C2[记录错误日志]
    C2 --> C3[提供重试选项]
    
    D --> D1[分析错误原因]
    D1 --> D2[尝试自动恢复]
    D2 --> D3{恢复成功?}
    D3 -->|是| D4[继续正常流程]
    D3 -->|否| D5[升级到通信层处理]
    
    E --> E1[检查连接状态]
    E1 --> E2[尝试重新连接]
    E2 --> E3{连接成功?}
    E3 -->|是| E4[重试原操作]
    E3 -->|否| E5[升级到系统层处理]
    
    F --> F1[记录致命错误]
    F1 --> F2[安全关闭组件]
    F2 --> F3[通知用户重启]
    
    style A fill:#ffcdd2
    style C3 fill:#fff3e0
    style D4 fill:#c8e6c9
    style E4 fill:#c8e6c9
    style F3 fill:#ffab91
```

### 连接恢复流程
```mermaid
stateDiagram-v2
    [*] --> 正常连接: 建立连接
    正常连接 --> 连接异常: 检测到错误
    连接异常 --> 重连尝试: 开始重连
    
    重连尝试 --> 连接成功: 重连成功
    重连尝试 --> 重连失败: 重连失败
    
    连接成功 --> 状态同步: 同步状态
    状态同步 --> 正常连接: 恢复完成
    
    重连失败 --> 等待重试: 指数退避
    等待重试 --> 重连尝试: 重试时间到
    
    重连失败 --> 连接放弃: 超过最大重试次数
    连接放弃 --> [*]: 通知用户手动重启
    
    note right of 重连尝试: 最多重试5次\n每次间隔递增
    note right of 状态同步: 恢复RPC状态\n重新激活扩展
```

## 🎯 用户使用场景流程

### 新用户首次使用
```mermaid
journey
    title 新用户首次使用RunVSAgent
    section 安装阶段
      下载插件: 3: 用户
      安装到IDE: 4: 用户
      重启IDE: 3: 用户
    section 首次启动
      检查环境: 5: 系统
      下载依赖: 4: 系统
      配置设置: 4: 用户
    section 初体验
      打开项目: 5: 用户
      使用AI助手: 9: 用户
      查看建议: 8: 用户
      应用代码: 9: 用户
    section 深度使用
      自定义设置: 7: 用户
      使用高级功能: 8: 用户
      分享经验: 9: 用户
```

### 日常开发使用流程
```mermaid
flowchart TD
    A[开始工作日] --> B[打开IDE]
    B --> C[RunVSAgent自动启动]
    C --> D[打开项目]
    D --> E[AI助手就绪]
    
    E --> F{开发任务类型}
    
    F -->|编写新功能| G[AI辅助编码]
    F -->|代码审查| H[AI代码分析]
    F -->|问题调试| I[AI调试助手]
    F -->|重构代码| J[AI重构建议]
    
    G --> G1[描述需求]
    G1 --> G2[AI生成代码]
    G2 --> G3[调整完善]
    G3 --> G4[集成到项目]
    
    H --> H1[选择代码段]
    H1 --> H2[AI分析质量]
    H2 --> H3[查看建议]
    H3 --> H4[应用改进]
    
    I --> I1[描述问题]
    I1 --> I2[AI分析错误]
    I2 --> I3[获取解决方案]
    I3 --> I4[修复问题]
    
    J --> J1[选择重构范围]
    J1 --> J2[AI生成计划]
    J2 --> J3[预览变更]
    J3 --> J4[执行重构]
    
    G4 --> K[继续开发]
    H4 --> K
    I4 --> K
    J4 --> K
    
    K --> L{工作完成?}
    L -->|否| F
    L -->|是| M[结束工作]
    
    style E fill:#c8e6c9
    style M fill:#ffeb3b
```

---

*本文档提供了RunVSAgent各个关键流程的可视化展示，帮助用户和开发者更好地理解系统的工作原理。*

*© 2025 WeCode-AI Team, Weibo Inc. | Apache License 2.0*