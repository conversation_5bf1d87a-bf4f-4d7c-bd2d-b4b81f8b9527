# RunVSAgent 流程图表文档

## 📋 目录

- [系统架构图](#系统架构图)
- [组件交互图](#组件交互图)
- [数据流程图](#数据流程图)
- [生命周期图](#生命周期图)
- [通信协议图](#通信协议图)
- [错误处理流程](#错误处理流程)

## 🏗️ 系统架构图

### 整体架构概览

```mermaid
graph TB
    subgraph "用户层"
        U[开发者]
    end
    
    subgraph "JetBrains IDE 进程"
        subgraph "UI层"
            UI[用户界面]
            TW[工具窗口]
            WV[WebView组件]
        end
        
        subgraph "插件核心层"
            PS[插件服务]
            EM[编辑器管理器]
            WM[WebView管理器]
            FM[文件监控器]
        end
        
        subgraph "通信层"
            RC[RPC客户端]
            SS[Socket服务器]
            PM[进程管理器]
        end
    end
    
    subgraph "Extension Host 进程"
        subgraph "运行时层"
            NR[Node.js运行时]
            TS[TypeScript引擎]
        end
        
        subgraph "API兼容层"
            VA[VSCode API]
            WS[工作区服务]
            LS[语言服务]
            DS[调试服务]
        end
        
        subgraph "扩展管理层"
            EM2[扩展管理器]
            RM[RPC管理器]
            SM[服务注册表]
        end
    end
    
    subgraph "VSCode扩展生态"
        RC2[Roo Code]
        LSP[语言服务器]
        DA[调试适配器]
        OE[其他扩展]
    end
    
    U --> UI
    UI --> TW
    UI --> WV
    TW --> PS
    WV --> WM
    PS --> EM
    PS --> FM
    EM --> RC
    WM --> RC
    FM --> RC
    RC <--> SS
    SS --> PM
    PM --> NR
    NR --> TS
    TS --> VA
    VA --> WS
    VA --> LS
    VA --> DS
    WS --> EM2
    LS --> EM2
    DS --> EM2
    EM2 --> RM
    RM --> SM
    SM --> RC2
    SM --> LSP
    SM --> DA
    SM --> OE
```

### 技术栈分层图

```mermaid
graph LR
    subgraph "前端技术栈"
        A[Kotlin/JVM]
        B[IntelliJ Platform SDK]
        C[JCEF WebView]
        D[Swing/AWT UI]
    end
    
    subgraph "后端技术栈"
        E[Node.js 18+]
        F[TypeScript 5.0+]
        G[VSCode API]
        H[RPC Protocol]
    end
    
    subgraph "通信技术栈"
        I[Unix Domain Socket]
        J[Named Pipe]
        K[MessagePack]
        L[JSON-RPC]
    end
    
    A --> B
    B --> C
    B --> D
    E --> F
    F --> G
    G --> H
    I --> K
    J --> K
    K --> L
    
    C <--> I
    D <--> J
```

## 🔄 组件交互图

### 插件启动交互序列

```mermaid
sequenceDiagram
    participant IDE as JetBrains IDE
    participant Plugin as Kotlin Plugin
    participant PM as Process Manager
    participant Host as Extension Host
    participant Ext as VSCode Extension
    
    Note over IDE,Ext: 插件启动阶段
    
    IDE->>Plugin: 项目打开事件
    Plugin->>Plugin: 初始化插件服务
    Plugin->>PM: 启动Extension Host进程
    PM->>Host: 创建Node.js进程
    Host->>Host: 初始化VSCode API层
    Host->>Plugin: 发送Ready信号
    Plugin->>Host: 发送初始化数据
    Host->>Ext: 激活扩展
    Ext->>Host: 注册服务和命令
    Host->>Plugin: 扩展就绪通知
    Plugin->>IDE: 显示UI界面
    
    Note over IDE,Ext: 启动完成
```

### RPC通信交互图

```mermaid
sequenceDiagram
    participant Client as RPC Client (Kotlin)
    participant Server as RPC Server (Node.js)
    participant Service as Service Implementation
    participant Callback as Callback Handler
    
    Note over Client,Callback: 请求-响应模式
    
    Client->>Server: 发送RPC请求
    Server->>Service: 调用服务方法
    Service->>Service: 执行业务逻辑
    Service->>Server: 返回执行结果
    Server->>Client: 发送RPC响应
    
    Note over Client,Callback: 事件通知模式
    
    Service->>Server: 触发事件
    Server->>Client: 发送事件通知
    Client->>Callback: 调用回调处理器
    Callback->>Client: 处理完成
```

## 📊 数据流程图

### 文档编辑同步流程

```mermaid
flowchart TD
    A[用户编辑文档] --> B{编辑器类型}
    B -->|JetBrains Editor| C[捕获编辑事件]
    B -->|WebView Editor| D[捕获Web事件]
    
    C --> E[Editor Bridge]
    D --> F[WebView Manager]
    
    E --> G[序列化变更数据]
    F --> G
    
    G --> H[RPC消息传输]
    H --> I[Extension Host接收]
    I --> J[VSCode API事件分发]
    J --> K[扩展处理变更]
    K --> L{需要反馈?}
    
    L -->|是| M[生成装饰器/诊断]
    L -->|否| N[处理完成]
    
    M --> O[RPC响应传输]
    O --> P[Plugin接收响应]
    P --> Q[更新UI显示]
    Q --> N
```

### 扩展加载流程

```mermaid
flowchart TD
    A[扩展发现] --> B[解析package.json]
    B --> C[验证扩展格式]
    C --> D{格式正确?}
    
    D -->|否| E[记录错误日志]
    D -->|是| F[创建扩展描述]
    
    F --> G[注册到扩展管理器]
    G --> H[检查激活事件]
    H --> I{需要激活?}
    
    I -->|否| J[等待激活事件]
    I -->|是| K[加载扩展主模块]
    
    K --> L[创建扩展上下文]
    L --> M[调用activate函数]
    M --> N{激活成功?}
    
    N -->|否| O[记录激活失败]
    N -->|是| P[注册扩展服务]
    
    P --> Q[扩展就绪]
    J --> R[监听激活事件]
    R --> K
    
    E --> S[扩展加载失败]
    O --> S
    Q --> T[扩展运行中]
```

## ⏱️ 生命周期图

### 插件生命周期

```mermaid
stateDiagram-v2
    [*] --> Initializing: IDE启动
    
    Initializing --> Starting: 插件加载
    Starting --> Connecting: 启动Extension Host
    Connecting --> Ready: 建立RPC连接
    
    Ready --> Active: 扩展激活完成
    Active --> Active: 正常运行
    
    Active --> Reconnecting: 连接断开
    Reconnecting --> Active: 重连成功
    Reconnecting --> Error: 重连失败
    
    Active --> Stopping: 项目关闭
    Error --> Stopping: 错误处理
    
    Stopping --> Cleanup: 清理资源
    Cleanup --> [*]: 插件卸载
    
    note right of Active
        - 处理用户交互
        - 同步编辑器状态
        - 管理扩展服务
    end note
    
    note right of Error
        - 记录错误日志
        - 尝试自动恢复
        - 用户错误提示
    end note
```

### 扩展生命周期

```mermaid
stateDiagram-v2
    [*] --> Discovered: 扩展发现
    
    Discovered --> Loading: 开始加载
    Loading --> Loaded: 加载完成
    Loading --> Failed: 加载失败
    
    Loaded --> Activating: 触发激活
    Activating --> Active: 激活成功
    Activating --> Failed: 激活失败
    
    Active --> Deactivating: 开始停用
    Deactivating --> Deactivated: 停用完成
    
    Failed --> Retrying: 重试加载
    Retrying --> Loading: 重新加载
    Retrying --> Abandoned: 放弃重试
    
    Deactivated --> [*]: 扩展卸载
    Abandoned --> [*]: 扩展废弃
    
    note right of Active
        - 提供扩展功能
        - 响应API调用
        - 处理事件通知
    end note
```

## 🔌 通信协议图

### RPC消息格式

```mermaid
classDiagram
    class RPCMessage {
        +MessageType type
        +number id
        +string method
        +any[] params
        +any result
        +RPCError error
    }
    
    class MessageType {
        <<enumeration>>
        REQUEST
        RESPONSE
        NOTIFICATION
    }
    
    class RPCError {
        +number code
        +string message
        +any data
    }
    
    class RPCRequest {
        +number id
        +string method
        +any[] params
    }
    
    class RPCResponse {
        +number id
        +any result
        +RPCError error
    }
    
    RPCMessage --> MessageType
    RPCMessage --> RPCError
    RPCRequest --|> RPCMessage
    RPCResponse --|> RPCMessage
```

### 服务代理模式

```mermaid
classDiagram
    class MainThreadService {
        <<interface>>
        +method1(params)
        +method2(params)
    }
    
    class ExtHostProxy {
        -rpcProtocol: IRPCProtocol
        -proxyId: ProxyIdentifier
        +method1(params)
        +method2(params)
        -invokeRemote(method, args)
    }
    
    class RPCProtocol {
        +getProxy(id): any
        +set(id, instance): void
        +call(id, method, args): Promise
    }
    
    class ServiceImplementation {
        +method1(params)
        +method2(params)
    }
    
    ExtHostProxy ..|> MainThreadService
    ServiceImplementation ..|> MainThreadService
    ExtHostProxy --> RPCProtocol
    RPCProtocol --> ServiceImplementation
```

## 🚨 错误处理流程

### 连接错误恢复

```mermaid
flowchart TD
    A[检测到连接错误] --> B[记录错误信息]
    B --> C[停止当前连接]
    C --> D[清理资源]
    D --> E[等待重连间隔]
    E --> F[尝试重新连接]
    F --> G{连接成功?}
    
    G -->|是| H[恢复服务状态]
    G -->|否| I{重试次数 < 最大值?}
    
    I -->|是| J[增加重试计数]
    I -->|否| K[标记为永久失败]
    
    J --> L[指数退避延迟]
    L --> F
    
    H --> M[通知连接恢复]
    K --> N[显示错误提示]
    
    M --> O[继续正常运行]
    N --> P[进入降级模式]
```

### 扩展错误处理

```mermaid
flowchart TD
    A[扩展运行错误] --> B{错误类型}
    
    B -->|加载错误| C[记录加载失败]
    B -->|运行时错误| D[捕获异常]
    B -->|API调用错误| E[验证参数]
    
    C --> F[标记扩展不可用]
    D --> G[隔离错误扩展]
    E --> H[返回错误响应]
    
    F --> I[显示错误通知]
    G --> J{错误严重程度}
    H --> K[记录API错误]
    
    J -->|轻微| L[继续运行其他功能]
    J -->|严重| M[重启扩展]
    J -->|致命| N[禁用扩展]
    
    I --> O[提供解决建议]
    L --> P[监控后续错误]
    M --> Q[清理扩展状态]
    N --> R[从服务中移除]
    
    Q --> S[重新激活扩展]
    S --> T{重启成功?}
    T -->|是| U[恢复正常运行]
    T -->|否| N
```

### 性能监控流程

```mermaid
flowchart TD
    A[性能监控启动] --> B[收集性能指标]
    B --> C{指标类型}
    
    C -->|内存使用| D[监控内存占用]
    C -->|CPU使用| E[监控CPU占用]
    C -->|响应时间| F[监控RPC延迟]
    C -->|错误率| G[统计错误频率]
    
    D --> H[检查内存阈值]
    E --> I[检查CPU阈值]
    F --> J[检查延迟阈值]
    G --> K[检查错误阈值]
    
    H --> L{超过阈值?}
    I --> L
    J --> L
    K --> L
    
    L -->|否| M[继续监控]
    L -->|是| N[触发性能警告]
    
    N --> O[记录性能问题]
    O --> P[执行优化策略]
    P --> Q{优化成功?}
    
    Q -->|是| R[恢复正常监控]
    Q -->|否| S[升级警告级别]
    
    S --> T[通知用户]
    T --> U[建议性能优化]
    
    M --> B
    R --> B
```

## 📈 性能监控图表

### 系统资源使用监控

```mermaid
graph LR
    subgraph "资源监控"
        A[CPU监控] --> D[性能指标收集器]
        B[内存监控] --> D
        C[网络监控] --> D
    end

    subgraph "阈值检查"
        D --> E{超过阈值?}
        E -->|是| F[触发警告]
        E -->|否| G[继续监控]
    end

    subgraph "优化策略"
        F --> H[内存清理]
        F --> I[连接池优化]
        F --> J[缓存策略调整]
    end

    G --> A
    H --> K[性能恢复]
    I --> K
    J --> K
    K --> A
```

### RPC通信性能分析

```mermaid
pie title RPC调用分布
    "文档同步" : 45
    "扩展API调用" : 25
    "UI更新" : 15
    "文件操作" : 10
    "其他服务" : 5
```

## 🔧 部署架构图

### 开发环境部署

```mermaid
graph TD
    subgraph "开发机器"
        A[JetBrains IDE]
        B[Extension Host Debug]
        C[VSCode扩展源码]
        D[热重载服务]
    end

    subgraph "调试工具"
        E[Chrome DevTools]
        F[Node.js Inspector]
        G[IDE调试器]
    end

    A <--> B
    B <--> C
    C <--> D
    D --> A

    B --> F
    A --> G
    C --> E
```

### 生产环境部署

```mermaid
graph TD
    subgraph "用户环境"
        A[JetBrains IDE]
        B[RunVSAgent插件]
        C[Extension Host运行时]
        D[VSCode扩展包]
    end

    subgraph "系统依赖"
        E[Node.js 18+]
        F[JDK 17+]
        G[系统库]
    end

    A --> B
    B --> C
    C --> D
    C --> E
    B --> F
    E --> G
```

## 📊 数据模型图

### 扩展描述数据模型

```mermaid
erDiagram
    Extension {
        string identifier
        string name
        string displayName
        string description
        string version
        string publisher
        string main
        array activationEvents
        object engines
        boolean isBuiltin
    }

    ExtensionContext {
        array subscriptions
        object workspaceState
        object globalState
        string extensionPath
        object logger
    }

    RPCProtocol {
        string protocolId
        object messageHandler
        object errorHandler
        boolean isDisposed
    }

    Extension ||--|| ExtensionContext : creates
    ExtensionContext ||--|| RPCProtocol : uses
```

### 配置数据模型

```mermaid
erDiagram
    PluginConfig {
        string debugMode
        string debugResource
        string vscodePlugin
        object platformSettings
    }

    ExtensionConfig {
        string extensionPath
        object packageJson
        array dependencies
        object contributes
    }

    RuntimeConfig {
        string nodeVersion
        string extensionHostPath
        object environmentVars
        array commandLineArgs
    }

    PluginConfig ||--o{ ExtensionConfig : manages
    ExtensionConfig ||--|| RuntimeConfig : requires
```

---

## 📚 图表使用指南

### 如何阅读架构图

1. **颜色编码**:
   - 蓝色: JetBrains IDE组件
   - 紫色: Extension Host组件
   - 绿色: VSCode扩展组件
   - 橙色: 通信层组件

2. **箭头含义**:
   - 实线箭头: 直接调用关系
   - 虚线箭头: 事件通知关系
   - 双向箭头: 双向通信关系

3. **子图分组**:
   - 按进程边界分组
   - 按功能模块分组
   - 按技术栈分组

### 图表更新维护

- 架构变更时及时更新图表
- 新增组件时补充相关图表
- 定期检查图表与代码的一致性
- 使用标准的Mermaid语法

---

这些图表提供了RunVSAgent系统的可视化表示，帮助开发者更好地理解系统架构、组件交互和数据流程。通过这些图表，可以快速掌握系统的工作原理和设计思路。
