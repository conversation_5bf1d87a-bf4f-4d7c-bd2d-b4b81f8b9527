# RunVSAgent - VSCode扩展跨IDE运行工具

[![License](https://img.shields.io/badge/License-Apache%202.0-blue.svg)](https://opensource.org/licenses/Apache-2.0)
[![Node.js](https://img.shields.io/badge/Node.js-18%2B-green.svg)](https://nodejs.org/)
[![JetBrains](https://img.shields.io/badge/JetBrains-IntelliJ%20Platform-orange.svg)](https://www.jetbrains.com/)

> **让VSCode扩展在任何IDE中运行的革命性工具**

## 🎯 项目简介

RunVSAgent 是一个创新的跨平台开发工具，它打破了IDE生态的壁垒，让开发者能够在JetBrains系列IDE（IntelliJ IDEA、WebStorm、PyCharm等）中无缝运行VSCode扩展和AI编程助手。

### 🌟 核心价值

- **统一开发体验**: 在熟悉的JetBrains IDE中享受VSCode丰富的扩展生态
- **零学习成本**: 无需切换IDE，保持原有的开发习惯和工作流
- **AI助手集成**: 支持Roo Code等先进的AI编程助手
- **跨平台兼容**: 支持Windows、macOS、Linux多平台运行

## 🚀 快速开始

### 系统要求

- **JetBrains IDE**: 2023.1或更高版本
- **Node.js**: 18.0或更高版本  
- **JDK**: 17或更高版本（仅开发时需要）
- **操作系统**: Windows 10+、macOS 10.15+、Ubuntu 18.04+

### 安装方式

#### 方式一：从GitHub Releases下载（推荐）

1. **下载插件包**
   - 访问 [GitHub Releases](https://github.com/wecode-ai/RunVSAgent/releases) 页面
   - 下载最新版本的 `.zip` 插件包

2. **安装到IDE**
   ```
   JetBrains IDE → Settings/Preferences → Plugins 
   → 齿轮图标 ⚙️ → Install Plugin from Disk...
   → 选择下载的.zip文件 → 重启IDE
   ```

3. **验证安装**
   - 重启后在插件列表中查看RunVSAgent
   - 右侧工具栏应出现RunVSAgent面板

#### 方式二：从源码构建

```bash
# 1. 克隆仓库
git clone https://github.com/wecode-ai/RunVSAgent.git
cd RunVSAgent

# 2. 环境初始化
./scripts/setup.sh

# 3. 构建项目
./scripts/build.sh

# 4. 构建JetBrains插件
cd jetbrains_plugin
./gradlew buildPlugin

# 5. 安装插件
# 插件文件位于: jetbrains_plugin/build/distributions/
# 按照方式一的步骤2进行安装
```

## 🎮 使用指南

### 基础使用

1. **启动RunVSAgent**
   - 打开JetBrains IDE
   - 在右侧工具栏点击RunVSAgent图标
   - 等待扩展主机初始化完成

2. **使用AI助手**
   - 在RunVSAgent面板中可以看到Roo Code界面
   - 直接在面板中与AI助手交互
   - 支持代码生成、重构、解释等功能

3. **扩展管理**
   - 扩展会自动加载和激活
   - 支持VSCode扩展的大部分API功能
   - 实时同步编辑器状态和项目信息

### 高级功能

- **多项目支持**: 每个项目独立的扩展实例
- **实时同步**: 编辑器内容、光标位置、选择区域实时同步
- **文件监控**: 自动监控项目文件变化
- **主题集成**: 支持JetBrains主题样式

## 🏗️ 技术架构

RunVSAgent采用分层架构设计，通过RPC通信实现VSCode扩展与JetBrains IDE的无缝集成：

```
┌─────────────────────────────────────────────────────────────┐
│                    JetBrains IDE                            │
├─────────────────────────────────────────────────────────────┤
│  Kotlin Plugin  │  UI Integration  │  Editor Bridge        │
├─────────────────────────────────────────────────────────────┤
│                    RPC Communication                        │
├─────────────────────────────────────────────────────────────┤
│  Extension Host │  VSCode API Layer │  Agent Manager       │
├─────────────────────────────────────────────────────────────┤
│                    VSCode Extensions                        │
└─────────────────────────────────────────────────────────────┘
```

### 核心组件

- **JetBrains Plugin**: Kotlin编写的IDE插件，负责UI集成和编辑器桥接
- **Extension Host**: Node.js运行时环境，提供VSCode API兼容层
- **RPC Manager**: 高性能进程间通信，实现实时数据交换
- **Agent Manager**: 扩展生命周期管理和服务协调

## 🔧 支持的IDE

### JetBrains系列

| IDE | 版本要求 | 支持状态 |
|-----|---------|---------|
| IntelliJ IDEA | 2023.1+ | ✅ 完全支持 |
| WebStorm | 2023.1+ | ✅ 完全支持 |
| PyCharm | 2023.1+ | ✅ 完全支持 |
| PhpStorm | 2023.1+ | ✅ 完全支持 |
| RubyMine | 2023.1+ | ✅ 完全支持 |
| CLion | 2023.1+ | ✅ 完全支持 |
| GoLand | 2023.1+ | ✅ 完全支持 |
| DataGrip | 2023.1+ | ✅ 完全支持 |
| Rider | 2023.1+ | ✅ 完全支持 |
| Android Studio | 2023.1+ | ✅ 完全支持 |

### 其他IDE

| IDE | 支持状态 |
|-----|---------|
| Xcode | 🚧 开发中 |
| Eclipse | 📋 计划中 |
| Visual Studio | 📋 计划中 |

## 🤖 支持的扩展

### AI编程助手

- **[Roo Code](https://roocode.com)**: 先进的AI代码生成和重构工具
- **GitHub Copilot**: 计划支持
- **Tabnine**: 计划支持

### 开发工具

- **语言服务器**: 支持大部分LSP扩展
- **调试器**: 支持VSCode调试协议
- **版本控制**: Git集成扩展
- **代码格式化**: Prettier、ESLint等

## 🛠️ 开发模式

对于插件开发者，RunVSAgent提供了便捷的开发模式：

```bash
# 启动Extension Host开发模式
cd extension_host
npm install
npm run dev

# 启动JetBrains插件开发模式
cd jetbrains_plugin
./gradlew runIde
```

## 📚 相关文档

- [技术架构详解](ARCHITECTURE.md) - 深入了解系统设计和实现原理
- [开发者指南](DEVELOPER_GUIDE.md) - 插件开发和贡献指南
- [流程图表](DIAGRAMS.md) - 可视化架构和流程图
- [API文档](docs/API.md) - 扩展开发API参考

## 🤝 贡献指南

我们欢迎社区贡献！请查看 [CONTRIBUTING.md](CONTRIBUTING.md) 了解详细的贡献流程。

### 贡献方式

1. **Bug报告**: 在GitHub Issues中报告问题
2. **功能建议**: 提出新功能需求
3. **代码贡献**: 提交Pull Request
4. **文档改进**: 完善项目文档

## 📄 许可证

本项目采用 Apache License 2.0 许可证。详见 [LICENSE](LICENSE) 文件。

## 👥 团队

### 核心贡献者
- **[Naituw](https://github.com/Naituw)** - 项目架构师
- [wayu002](https://github.com/wayu002) - 核心开发者
- [joker535](https://github.com/joker535) - 核心开发者
- [andrewzq777](https://github.com/andrewzq777) - 核心开发者

### 维护团队
- **组织**: WeCode-AI Team, Weibo Inc.
- **联系**: [GitHub Issues](https://github.com/wecode-ai/RunVSAgent/issues)
- **官网**: [https://weibo.com](https://weibo.com)

---

**Made with ❤️ by WeCode-AI Team**

> 让每个开发者都能享受最好的开发工具，不受IDE限制。
