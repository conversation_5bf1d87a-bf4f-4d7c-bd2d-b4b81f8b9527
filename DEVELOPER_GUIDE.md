# RunVSAgent 开发者指南

## 📋 目录

- [开发环境搭建](#开发环境搭建)
- [项目结构详解](#项目结构详解)
- [核心代码分析](#核心代码分析)
- [调试指南](#调试指南)
- [测试策略](#测试策略)
- [贡献流程](#贡献流程)
- [发布流程](#发布流程)

## 🛠️ 开发环境搭建

### 系统要求

```bash
# 必需软件
Node.js >= 18.0.0
JDK >= 17
Git >= 2.20
JetBrains IDE >= 2023.1

# 推荐工具
VS Code (用于TypeScript开发)
IntelliJ IDEA (用于Kotlin开发)
```

### 环境初始化

```bash
# 1. 克隆项目
git clone https://github.com/wecode-ai/RunVSAgent.git
cd RunVSAgent

# 2. 初始化开发环境
./scripts/setup.sh

# 3. 安装依赖
cd extension_host
npm install

# 4. 构建项目
npm run build

# 5. 验证环境
./scripts/test.sh
```

### IDE配置

#### VS Code配置 (TypeScript开发)
```json
// .vscode/settings.json
{
    "typescript.preferences.importModuleSpecifier": "relative",
    "typescript.suggest.autoImports": true,
    "editor.formatOnSave": true,
    "editor.codeActionsOnSave": {
        "source.organizeImports": true
    }
}
```

#### IntelliJ IDEA配置 (Kotlin开发)
```kotlin
// 推荐插件
- Kotlin
- Gradle
- Git
- Detekt (代码质量检查)
```

## 📁 项目结构详解

```
RunVSAgent/
├── extension_host/              # Node.js扩展主机
│   ├── src/                    # TypeScript源码
│   │   ├── main.ts            # 主入口点
│   │   ├── extensionManager.ts # 扩展管理器
│   │   ├── rpcManager.ts      # RPC通信管理
│   │   ├── webViewManager.ts  # WebView管理
│   │   └── config.ts          # 配置管理
│   ├── vscode/                # VSCode源码依赖
│   ├── package.json           # Node.js依赖配置
│   └── tsconfig.json          # TypeScript配置
├── jetbrains_plugin/          # JetBrains插件
│   ├── src/main/kotlin/       # Kotlin源码
│   │   └── com/sina/weibo/agent/
│   │       ├── core/          # 核心功能
│   │       ├── actors/        # RPC服务实现
│   │       ├── editor/        # 编辑器集成
│   │       ├── webview/       # WebView支持
│   │       ├── workspace/     # 工作区管理
│   │       └── plugin/        # 插件生命周期
│   ├── src/main/resources/    # 资源文件
│   │   ├── META-INF/plugin.xml # 插件配置
│   │   └── themes/            # 主题文件
│   └── build.gradle.kts       # Gradle构建配置
├── deps/                      # 依赖项目
│   ├── vscode/               # VSCode源码
│   └── roo-code/             # Roo Code扩展
├── scripts/                   # 构建脚本
│   ├── setup.sh              # 环境初始化
│   ├── build.sh              # 项目构建
│   ├── test.sh               # 测试运行
│   └── clean.sh              # 清理脚本
└── docs/                      # 文档目录
```

## 🔍 核心代码分析

### Extension Host核心组件

#### 1. ExtensionManager (扩展管理器)

```typescript
// extension_host/src/extensionManager.ts
export class ExtensionManager {
    private extensionDescriptions: Map<string, IExtensionDescription> = new Map();

    /**
     * 注册扩展
     * @param extensionName 扩展名称
     * @returns 扩展注册信息
     */
    public registerExtension(extensionName: string): ExtensionRegistration {
        const extensionPath = this.findExtensionPath(extensionName);
        const description = this.parseExtensionDescription(extensionPath);
        
        this.extensionDescriptions.set(extensionName, description);
        
        return {
            identifier: description.identifier,
            path: extensionPath,
            description: description
        };
    }

    /**
     * 激活扩展
     * @param extensionId 扩展ID
     * @param rpcProtocol RPC协议实例
     */
    public async activateExtension(extensionId: string, rpcProtocol: IRPCProtocol): Promise<void> {
        const description = this.extensionDescriptions.get(extensionId);
        if (!description) {
            throw new Error(`Extension ${extensionId} not found`);
        }

        // 加载扩展主模块
        const extensionMain = await import(path.join(description.extensionLocation.fsPath, description.main));
        
        // 创建扩展上下文
        const context = this.createExtensionContext(description, rpcProtocol);
        
        // 激活扩展
        await extensionMain.activate(context);
    }
}
```

#### 2. RPCManager (RPC通信管理)

```typescript
// extension_host/src/rpcManager.ts
export class RPCManager {
    private rpcProtocol: IRPCProtocol;
    private extensionManager: ExtensionManager;

    constructor(protocol: PersistentProtocol, extensionManager: ExtensionManager) {
        this.extensionManager = extensionManager;
        this.rpcProtocol = new RPCProtocol(protocol, new FileRPCProtocolLogger());
        this.setupMainThreadServices();
    }

    /**
     * 设置主线程服务
     */
    private setupMainThreadServices(): void {
        // 注册扩展主机服务
        this.rpcProtocol.set(ExtHostContext.ExtHostCommands, new ExtHostCommands(this.rpcProtocol));
        this.rpcProtocol.set(ExtHostContext.ExtHostWorkspace, new ExtHostWorkspace(this.rpcProtocol));
        this.rpcProtocol.set(ExtHostContext.ExtHostLanguages, new ExtHostLanguages(this.rpcProtocol));
    }

    /**
     * 获取RPC协议实例
     */
    public getRPCProtocol(): IRPCProtocol {
        return this.rpcProtocol;
    }
}
```

### JetBrains Plugin核心组件

#### 1. WecoderPluginService (插件服务)

```kotlin
// jetbrains_plugin/src/main/kotlin/com/sina/weibo/agent/plugin/WecoderPluginService.kt
@Service(Service.Level.PROJECT)
class WecoderPluginService(private var currentProject: Project) : Disposable {
    private val logger = Logger.getInstance(WecoderPluginService::class.java)
    
    // 服务实例
    private val socketServer = ExtensionSocketServer()
    private val processManager = ExtensionProcessManager()
    private val workspaceManager = WorkspaceFileChangeManager()

    /**
     * 初始化插件服务
     */
    fun initialize(project: Project) {
        coroutineScope.launch {
            try {
                val projectPath = project.basePath ?: return@launch
                
                // 启动Socket服务器
                val server: ISocketServer = if (SystemInfo.isWindows) socketServer else udsSocketServer
                val portOrPath = server.start(projectPath)
                
                // 启动扩展进程
                if (processManager.start(portOrPath)) {
                    isInitialized = true
                    logger.info("WecoderPluginService initialization completed")
                }
            } catch (e: Exception) {
                logger.error("Error during initialization", e)
            }
        }
    }
}
```

#### 2. ExtensionProcessManager (进程管理器)

```kotlin
// jetbrains_plugin/src/main/kotlin/com/sina/weibo/agent/core/ExtensionProcessManager.kt
class ExtensionProcessManager {
    private var extensionProcess: Process? = null
    
    /**
     * 启动扩展进程
     */
    fun start(portOrPath: String): Boolean {
        try {
            val entryFile = findExtensionEntryFile() ?: return false
            val nodeModulesPath = findNodeModulesPath() ?: return false
            
            val processBuilder = ProcessBuilder().apply {
                command(listOf("node", entryFile))
                environment().apply {
                    put("VSCODE_EXTHOST_WILL_SEND_SOCKET", "1")
                    put("VSCODE_EXTHOST_SOCKET_HOST", "127.0.0.1")
                    put("VSCODE_EXTHOST_SOCKET_PORT", portOrPath)
                    put("NODE_PATH", nodeModulesPath)
                }
            }
            
            extensionProcess = processBuilder.start()
            return true
        } catch (e: Exception) {
            logger.error("Failed to start extension process", e)
            return false
        }
    }
}
```

## 🐛 调试指南

### Extension Host调试

#### 1. 启动调试模式

```bash
# 设置调试环境变量
export VSCODE_DEBUG=true

# 启动Extension Host
cd extension_host
npm run dev
```

#### 2. VS Code调试配置

```json
// .vscode/launch.json
{
    "version": "0.2.0",
    "configurations": [
        {
            "name": "Debug Extension Host",
            "type": "node",
            "request": "attach",
            "port": 9229,
            "restart": true,
            "localRoot": "${workspaceFolder}/extension_host",
            "remoteRoot": "${workspaceFolder}/extension_host"
        }
    ]
}
```

#### 3. 日志调试

```typescript
// 启用详细日志
const logger = new ConsoleLogger();
logger.setLevel(LogLevel.Trace);

// 在关键位置添加日志
logger.info('Extension activation started', { extensionId });
logger.debug('RPC message received', { message });
logger.error('Extension activation failed', { error });
```

### JetBrains Plugin调试

#### 1. 开发模式运行

```bash
cd jetbrains_plugin
./gradlew runIde
```

#### 2. 远程调试配置

```kotlin
// 在代码中设置断点
class WecoderPluginService {
    fun initialize(project: Project) {
        // 设置断点调试初始化流程
        val projectPath = project.basePath
        logger.info("Initializing plugin for project: $projectPath")
    }
}
```

#### 3. 日志配置

```xml
<!-- jetbrains_plugin/src/main/resources/META-INF/plugin.xml -->
<idea-plugin>
    <extensions defaultExtensionNs="com.intellij">
        <applicationService 
            serviceImplementation="com.sina.weibo.agent.util.LoggerService"/>
    </extensions>
</idea-plugin>
```

### 通信调试

#### 1. RPC消息跟踪

```typescript
// Extension Host端
class DebugRPCProtocol implements IRPCProtocol {
    send(msg: any): void {
        console.log('Sending RPC message:', JSON.stringify(msg, null, 2));
        this.actualProtocol.send(msg);
    }
    
    onMessage(callback: (msg: any) => void): void {
        this.actualProtocol.onMessage((msg) => {
            console.log('Received RPC message:', JSON.stringify(msg, null, 2));
            callback(msg);
        });
    }
}
```

```kotlin
// JetBrains Plugin端
class DebugMessagePassingProtocol : IMessagePassingProtocol {
    override fun send(buffer: ByteArray) {
        logger.debug("Sending message: ${buffer.size} bytes")
        actualProtocol.send(buffer)
    }
    
    override fun onMessage(callback: (ByteArray) -> Unit) {
        actualProtocol.onMessage { buffer ->
            logger.debug("Received message: ${buffer.size} bytes")
            callback(buffer)
        }
    }
}
```

## 🧪 测试策略

### 单元测试

#### Extension Host测试

```typescript
// extension_host/test/extensionManager.test.ts
import { ExtensionManager } from '../src/extensionManager';

describe('ExtensionManager', () => {
    let extensionManager: ExtensionManager;
    
    beforeEach(() => {
        extensionManager = new ExtensionManager();
    });
    
    test('should register extension successfully', () => {
        const registration = extensionManager.registerExtension('test-extension');
        expect(registration.identifier.value).toBe('test-extension');
    });
    
    test('should activate extension with valid context', async () => {
        const mockRPCProtocol = createMockRPCProtocol();
        await extensionManager.activateExtension('test-extension', mockRPCProtocol);
        // 验证激活结果
    });
});
```

#### JetBrains Plugin测试

```kotlin
// jetbrains_plugin/src/test/kotlin/ExtensionProcessManagerTest.kt
class ExtensionProcessManagerTest {
    private lateinit var processManager: ExtensionProcessManager
    
    @BeforeEach
    fun setUp() {
        processManager = ExtensionProcessManager()
    }
    
    @Test
    fun `should start extension process successfully`() {
        val result = processManager.start("12345")
        assertTrue(result)
    }
    
    @Test
    fun `should handle process startup failure gracefully`() {
        // 模拟启动失败场景
        val result = processManager.start("invalid-port")
        assertFalse(result)
    }
}
```

### 集成测试

```bash
# 运行完整测试套件
./scripts/test.sh

# 运行特定测试
cd extension_host
npm test -- --grep "ExtensionManager"

cd jetbrains_plugin
./gradlew test --tests "*ExtensionProcessManagerTest*"
```

## 🤝 贡献流程

### 1. 准备工作

```bash
# Fork项目到个人账户
# 克隆Fork的仓库
git clone https://github.com/YOUR_USERNAME/RunVSAgent.git
cd RunVSAgent

# 添加上游仓库
git remote add upstream https://github.com/wecode-ai/RunVSAgent.git
```

### 2. 开发流程

```bash
# 创建功能分支
git checkout -b feature/your-feature-name

# 进行开发
# ... 编写代码 ...

# 运行测试
./scripts/test.sh

# 提交更改
git add .
git commit -m "feat: add your feature description"

# 推送到Fork仓库
git push origin feature/your-feature-name
```

### 3. 代码规范

#### TypeScript规范
```typescript
// 使用明确的类型注解
function processExtension(extension: IExtensionDescription): Promise<void> {
    // 实现逻辑
}

// 使用接口定义契约
interface ExtensionActivationContext {
    subscriptions: Disposable[];
    workspaceState: Memento;
    globalState: Memento;
}
```

#### Kotlin规范
```kotlin
// 使用数据类
data class ExtensionInfo(
    val id: String,
    val name: String,
    val version: String
)

// 使用协程处理异步操作
suspend fun activateExtension(extensionId: String): Result<Unit> {
    return try {
        // 异步激活逻辑
        Result.success(Unit)
    } catch (e: Exception) {
        Result.failure(e)
    }
}
```

### 4. 提交Pull Request

1. 确保所有测试通过
2. 更新相关文档
3. 填写详细的PR描述
4. 等待代码审查

## 📦 发布流程

### 1. 版本管理

```bash
# 更新版本号
# extension_host/package.json
# jetbrains_plugin/gradle.properties

# 创建版本标签
git tag -a v1.0.0 -m "Release version 1.0.0"
git push upstream v1.0.0
```

### 2. 构建发布包

```bash
# 构建Extension Host
cd extension_host
npm run build

# 构建JetBrains Plugin
cd jetbrains_plugin
./gradlew buildPlugin

# 发布包位于: jetbrains_plugin/build/distributions/
```

### 3. 发布到市场

```bash
# 发布到JetBrains Marketplace
./gradlew publishPlugin

# 创建GitHub Release
# 上传构建产物到GitHub Releases
```

---

通过遵循这个开发者指南，您可以高效地参与RunVSAgent项目的开发，为跨IDE扩展生态做出贡献。
