<!--
SPDX-FileCopyrightText: 2025 Weibo, Inc.

SPDX-License-Identifier: Apache-2.0
-->

<!-- Plugin Configuration File. Read more: https://plugins.jetbrains.com/docs/intellij/plugin-configuration-file.html -->
<idea-plugin>
    <!-- Unique identifier of the plugin. It should be FQN. It cannot be changed between the plugin versions. -->
    <id>RunVSAgent</id>

    <!-- Public plugin name should be written in Title Case.
         Guidelines: https://plugins.jetbrains.com/docs/marketplace/plugin-overview-page.html#plugin-name -->
    <name>RunVSAgent</name>

    <!-- A displayed Vendor name or Organization ID displayed on the Plugins Page. -->
    <vendor url="https://weibo.com">WeCode-AI</vendor>

    <!-- Description of the plugin displayed on the Plugin Page and IDE Plugin Manager.
         Simple HTML elements (text formatting, paragraphs, and lists) can be added inside of <![CDATA[ ]]> tag.
         Guidelines: https://plugins.jetbrains.com/docs/marketplace/plugin-overview-page.html#plugin-description -->
    <description><![CDATA[
        <h1 id="wecoder-plugin-for-intellij-platform">Run VSCode Agent Plugin for IntelliJ Platform</h1>
        <p>A whole dev team of AI agents in your editor.</p>
        <br/>
        <h2 id="requirements">Requirements</h2>
        requires <a href="https://nodejs.org/">Node.js</a> v18+ installed. </p>
  ]]></description>

    <!-- Product and plugin compatibility requirements.
         Read more: https://plugins.jetbrains.com/docs/intellij/plugin-compatibility.html -->
    <depends>com.intellij.modules.platform</depends>
    <depends>org.jetbrains.plugins.terminal</depends>

    <!-- Extension points defined by the plugin.
         Read more: https://plugins.jetbrains.com/docs/intellij/plugin-extension-points.html -->
    <extensions defaultExtensionNs="com.intellij">
        <projectService serviceImplementation="com.sina.weibo.agent.plugin.WecoderPluginService"/>
        <postStartupActivity implementation="com.sina.weibo.agent.plugin.WecoderPlugin"/>
        <editorFactoryListener implementation="com.sina.weibo.agent.editor.EditorListener"/>
        <toolWindow factoryClass="com.sina.weibo.agent.ui.RooToolWindowFactory"
                    id="RunVSAgent"
                    secondary="true"
                    icon="/icons/wecoder-window.svg"
                    anchor="right" />
        <notificationGroup id="RunVSAgent"
                           displayType="BALLOON"/>
    </extensions>

    <extensions defaultExtensionNs="org.jetbrains.plugins.terminal">
        <localTerminalCustomizer implementation="com.sina.weibo.agent.terminal.WeCoderTerminalCustomizer"/>
    </extensions>

    <actions>
        <action id="RunVSAgent.plusButtonClicked"
                icon="AllIcons.General.Add"
                class="com.sina.weibo.agent.actions.PlusButtonClickAction"
                text="New Task"
                description="New task">
            <override-text place="GoToAction" text="New Task" />
        </action>

        <action id="RunVSAgent.promptsButtonClicked"
                icon="AllIcons.General.Information"
                class="com.sina.weibo.agent.actions.PromptsButtonClickAction"
                text="Prompt"
                description="Prompts">
            <override-text place="GoToAction" text="Prompts" />
        </action>

        <action id="RunVSAgent.mcpButtonClicked"
                icon="AllIcons.Webreferences.Server"
                class="com.sina.weibo.agent.actions.MCPButtonClickAction"
                text="MCP Server"
                description="MCP server">
            <override-text place="GoToAction" text="MCP Server" />
        </action>

        <action id="RunVSAgent.historyButtonClicked"
                icon="AllIcons.Vcs.History"
                class="com.sina.weibo.agent.actions.HistoryButtonClickAction"
                text="History"
                description="History">
            <override-text place="GoToAction" text="History" />
        </action>

        <action id="RunVSAgent.marketplaceButtonClicked"
                icon="AllIcons.Actions.Install"
                class="com.sina.weibo.agent.actions.MarketplaceButtonClickAction"
                text="MCP Marketplace"
                description="Marketplace">
            <override-text place="GoToAction" text="Marketplace" />
        </action>

        <action id="RunVSAgent.settingsButtonClicked"
                icon="AllIcons.General.Settings"
                class="com.sina.weibo.agent.actions.SettingsButtonClickAction"
                text="Settings"
                description="Setting">
            <override-text place="GoToAction" text="Setting" />
        </action>

        <group id="WecoderToolbarGroup">
            <reference ref="RunVSAgent.plusButtonClicked" />
            <reference ref="RunVSAgent.promptsButtonClicked" />
            <reference ref="RunVSAgent.mcpButtonClicked" />
            <reference ref="RunVSAgent.historyButtonClicked" />
            <reference ref="RunVSAgent.marketplaceButtonClicked" />
            <reference ref="RunVSAgent.settingsButtonClicked" />
        </group>

        <group id="RunVSAgent.RightClickMenu" text="RunVSAgent" description="RunVSAgent main menu"  icon="/icons/weibo_logo_13px.svg" popup="true">
            <group id="RunVSAgent.RightClick.Chat" class="com.sina.weibo.agent.actions.RightClickChatActionGroup"
                   text="Chat" description="RunVSAgent chat tool"/>
            <add-to-group group-id="EditorPopupMenu" anchor="first"/>
        </group>
    </actions>
</idea-plugin>